// Test script for background question generation
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testBackgroundGeneration() {
  console.log('🧪 Testing Background Question Generation System\n');
  
  try {
    // Test 1: Role validation (should trigger background generation)
    console.log('1️⃣  Testing role validation...');
    const roleResponse = await axios.post(`${BASE_URL}/api/validate-role`, {
      role: 'Account Manager'
    });
    console.log('✅ Role validation response:', roleResponse.data);
    
    // Wait a moment for background generation to start
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test 2: Check background generation status
    console.log('\n2️⃣  Checking background generation status...');
    const statusResponse = await axios.get(`${BASE_URL}/api/background-generation-status/Account Manager`);
    console.log('📊 Background generation status:', statusResponse.data);
    
    // Test 3: Framework access (should also trigger if not already triggered)
    console.log('\n3️⃣  Testing framework access...');
    const frameworkResponse = await axios.post(`${BASE_URL}/api/generate-framework`, {
      role: 'Account Manager',
      email: '<EMAIL>'
    });
    console.log('✅ Framework response received (length):', JSON.stringify(frameworkResponse.data).length);
    
    // Test 4: Monitor background generation progress
    console.log('\n4️⃣  Monitoring background generation progress...');
    let attempts = 0;
    const maxAttempts = 10;
    
    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const progressResponse = await axios.get(`${BASE_URL}/api/background-generation-status/Account Manager`);
      const status = progressResponse.data;
      
      console.log(`📈 Progress check ${attempts + 1}:`, {
        completed: status.completed,
        completionPercentage: status.completionPercentage,
        sections: Object.keys(status.sections || {}).length
      });
      
      if (status.completed) {
        console.log('🎉 Background generation completed!');
        console.log('📋 Final status:', status);
        break;
      }
      
      attempts++;
    }
    
    if (attempts >= maxAttempts) {
      console.log('⏰ Timeout reached - background generation may still be in progress');
    }
    
    // Test 5: Test question loading with background questions
    console.log('\n5️⃣  Testing question loading...');
    
    // Test regular questions
    const quizResponse = await axios.post(`${BASE_URL}/api/generate-quiz`, {
      role: 'Account Manager',
      section: 'foundational',
      framework: frameworkResponse.data,
      email: '<EMAIL>'
    });
    console.log('✅ Quiz questions loaded:', quizResponse.data.length, 'questions');
    
    // Test self-assessment questions
    const selfAssessmentResponse = await axios.post(`${BASE_URL}/api/generate-self-assessment`, {
      role: 'Account Manager',
      section: 'foundational',
      framework: frameworkResponse.data,
      email: '<EMAIL>'
    });
    console.log('✅ Self-assessment questions loaded:', selfAssessmentResponse.data.length, 'questions');
    
    console.log('\n🎯 Test Summary:');
    console.log('- Role validation: ✅');
    console.log('- Background generation trigger: ✅');
    console.log('- Status monitoring: ✅');
    console.log('- Framework access: ✅');
    console.log('- Question loading: ✅');
    console.log('\n🚀 Background generation system is working!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
if (require.main === module) {
  testBackgroundGeneration();
}

module.exports = { testBackgroundGeneration };
