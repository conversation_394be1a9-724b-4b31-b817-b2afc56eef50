(function(global) {
    // Client-side request tracking to prevent duplicate requests
    const pendingRequests = new Set();

    // Global framework cache to share between components
    if (!window.frameworkCache) {
      window.frameworkCache = {};
    }

    // Global question cache to prevent duplicate API requests
    if (!window.questionCache) {
      window.questionCache = {};
    }

    // Background generation status tracking
    let backgroundGenerationCheckInterval = null;
    let backgroundGenerationStatus = null;

    // Helper function to check if a request is pending
    function isRequestPending(endpoint, params) {
      const requestKey = `${endpoint}_${JSON.stringify(params)}`;
      return pendingRequests.has(requestKey);
    }

    // Helper function to mark a request as pending
    function markRequestPending(endpoint, params) {
      const requestKey = `${endpoint}_${JSON.stringify(params)}`;
      pendingRequests.add(requestKey);
      console.log(`Request marked as pending: ${requestKey}`);
      return requestKey;
    }

    // Helper function to mark a request as completed
    function markRequestCompleted(requestKey) {
      pendingRequests.delete(requestKey);
      console.log(`Request marked as completed: ${requestKey}`);
    }

    // Helper function to cache framework data
    function cacheFramework(role, framework) {
      window.frameworkCache[role] = framework;
      console.log(`Framework cached for role: ${role}`);
    }

    // Helper function to get cached framework data
    function getCachedFramework(role) {
      return window.frameworkCache[role];
    }

    // Helper function to cache questions
    function cacheQuestions(role, section, type, questions) {
      const cacheKey = `${role}_${section}_${type}`;
      window.questionCache[cacheKey] = questions;
      console.log(`Cached ${type} questions for ${role}/${section}`);
    }

    // Helper function to get cached questions
    function getCachedQuestions(role, section) {
      const cacheKey = `${role}_${section}_regular`;
      return window.questionCache[cacheKey];
    }

    // Helper function to get cached self-assessment questions
    function getCachedSelfAssessmentQuestions(role, section) {
      const cacheKey = `${role}_${section}_self-assessment`;
      return window.questionCache[cacheKey];
    }

    // Function to check background generation status
    async function checkBackgroundGenerationStatus(role) {
      try {
        const response = await fetch(`/api/background-generation-status/${encodeURIComponent(role)}`);
        const status = await response.json();

        if (status.exists) {
          backgroundGenerationStatus = status;
          console.log(`Background generation status for ${role}:`, status);
          return status;
        }

        return null;
      } catch (error) {
        console.warn('Error checking background generation status:', error);
        return null;
      }
    }

    // Function to normalize section names (client-side)
    function normalizeSection(section) {
      const sectionMap = {
        'Essentials': 'foundational',
        'Foundational': 'foundational',
        'foundational': 'foundational',
        'Intermediate': 'intermediate',
        'intermediate': 'intermediate',
        'Advanced': 'advanced',
        'advanced': 'advanced',
        'Champions': 'advanced',
        'Expert': 'advanced'
      };

      const normalized = sectionMap[section] || section.toLowerCase();
      console.log(`🔄 Client-side section normalized: ${section} -> ${normalized}`);
      return normalized;
    }

    // Function to check if questions are available from background generation
    async function checkBackgroundQuestions(role, section) {
      try {
        // Normalize section name for consistent cache lookup
        const normalizedSection = normalizeSection(section);

        // Check if we have cached questions from background generation
        const regularCached = getCachedQuestions(role, normalizedSection);
        const selfAssessmentCached = getCachedSelfAssessmentQuestions(role, normalizedSection);

        if (regularCached && selfAssessmentCached) {
          console.log(`✅ Found cached questions from background generation for ${role}/${normalizedSection}`);
          return {
            regular: regularCached,
            selfAssessment: selfAssessmentCached,
            fromBackground: true
          };
        }

        console.log(`❌ No cached questions found for ${role}/${normalizedSection}`);
        return null;
      } catch (error) {
        console.warn('Error checking background questions:', error);
        return null;
      }
    }

    // Function to start monitoring background generation
    function startBackgroundGenerationMonitoring(role) {
      if (backgroundGenerationCheckInterval) {
        clearInterval(backgroundGenerationCheckInterval);
      }

      console.log(`Starting background generation monitoring for role: ${role}`);

      // Show background generation status
      showBackgroundGenerationStatus();

      backgroundGenerationCheckInterval = setInterval(async () => {
        const status = await checkBackgroundGenerationStatus(role);

        if (status && status.completed) {
          console.log(`Background generation completed for ${role}`);
          clearInterval(backgroundGenerationCheckInterval);
          backgroundGenerationCheckInterval = null;

          // Hide background generation status
          hideBackgroundGenerationStatus();

          // Show completion message briefly
          showBackgroundGenerationComplete();
        } else if (status) {
          // Update progress if available
          updateBackgroundGenerationProgress(status.completionPercentage || 0);
        }
      }, 3000); // Check every 3 seconds
    }

    // Function to show background generation status
    function showBackgroundGenerationStatus() {
      const statusElement = document.getElementById('background-generation-status');
      if (statusElement) {
        statusElement.classList.remove('hidden');
      }
    }

    // Function to hide background generation status
    function hideBackgroundGenerationStatus() {
      const statusElement = document.getElementById('background-generation-status');
      if (statusElement) {
        statusElement.classList.add('hidden');
      }
    }

    // Function to update background generation progress
    function updateBackgroundGenerationProgress(percentage) {
      const textElement = document.getElementById('background-generation-text');
      if (textElement) {
        textElement.textContent = `Preparing questions... ${percentage}%`;
      }
    }

    // Function to show completion message
    function showBackgroundGenerationComplete() {
      const textElement = document.getElementById('background-generation-text');
      if (textElement) {
        textElement.textContent = 'Questions ready! Assessment will load instantly.';

        // Hide after 3 seconds
        setTimeout(() => {
          hideBackgroundGenerationStatus();
        }, 3000);
      }
    }

    // Function to wait for background generation to complete
    async function waitForBackgroundGeneration(role, section) {
      console.log(`⏳ Waiting for background generation to complete for ${role}...`);

      // Update loading state to show we're waiting
      updateLoadingState(LoadingState.LOADING_FIRST_BATCH, {
        customMessage: 'Finalizing questions...',
        customSubmessage: 'Background generation in progress'
      });

      const maxWaitTime = 30000; // 30 seconds timeout
      const pollInterval = 2000; // Check every 2 seconds
      const startTime = Date.now();

      return new Promise((resolve) => {
        const checkStatus = async () => {
          const elapsed = Date.now() - startTime;

          if (elapsed >= maxWaitTime) {
            console.log('⏰ Background generation wait timeout reached');
            updateLoadingState(LoadingState.LOADING_FIRST_BATCH, {
              customMessage: 'Taking longer than expected...',
              customSubmessage: 'Switching to standard loading'
            });
            resolve({ success: false, reason: 'timeout' });
            return;
          }

          try {
            const status = await checkBackgroundGenerationStatus(role);

            if (!status || !status.exists) {
              console.log('❌ Background generation status not found');
              resolve({ success: false, reason: 'not_found' });
              return;
            }

            if (status.completed) {
              console.log('✅ Background generation completed!');

              // Check if questions are now available
              const questions = await checkBackgroundQuestions(role, section);
              if (questions) {
                resolve({ success: true, questions });
              } else {
                console.log('⚠️ Background generation completed but questions not found');
                resolve({ success: false, reason: 'questions_not_found' });
              }
              return;
            }

            // Update progress if available
            if (status.completionPercentage) {
              const progressMessage = `Finalizing questions... ${status.completionPercentage}%`;
              updateLoadingState(LoadingState.LOADING_FIRST_BATCH, {
                customMessage: progressMessage,
                customSubmessage: `${Math.ceil((maxWaitTime - elapsed) / 1000)}s remaining`
              });
            }

            // Continue waiting
            setTimeout(checkStatus, pollInterval);

          } catch (error) {
            console.error('Error checking background generation status:', error);
            resolve({ success: false, reason: 'error', error });
          }
        };

        // Start checking
        checkStatus();
      });
    }

    const startQuiz = () => {
        // Reset all scores and global state variables
        currentQuestion = 0;
        score = 0;
        sectionScores = {
          "Essentials": 0,
          "Intermediate": 0,
          "Advanced": 0,
          "Champions": 0
        };
        questionsPerSection = {
          "Essentials": 0,
          "Intermediate": 0,
          "Advanced": 0,
          "Champions": 0
        };
        isFinalSuccessContainerDisplayed = false;
        isFailureContainerDisplayed = false;

        document.getElementById("start-page").style.display = "none";
        document.getElementById("consent-popup").style.display = "block";
      };


const endQuiz = async () => {
  const finalResults = window.quizLogger ? window.quizLogger.logFinalResults() : null;
  document.getElementById("quiz-container").style.display = "none";

  // Calculate score based only on knowledge-check questions
  const knowledgeCheckQuestions = quizData.filter(q => q.type === "knowledge-check");
  const totalKnowledgeQuestions = knowledgeCheckQuestions.length;
  const passThreshold = 0.7 * totalKnowledgeQuestions;

  if (score >= passThreshold) {
    if (currentSection === 4) {
        const email = document.getElementById("email").value.trim();
        if (email) {
            try {
                await db.collection('assessmentStatus').doc(email).update({
                    status: 'completed',
                    timestamp: firebase.firestore.FieldValue.serverTimestamp()
                });
            } catch (error) {
                console.error('Error updating assessment status to Firestore:', error);
                showNotification('Error updating assessment status. Please try again.', 'error');
            }
        }
        isFinalSuccessContainerDisplayed = true;
        document.getElementById("final-success-container").style.display = "block";
    } else {
        document.getElementById("success-container").style.display = "block";
        document.getElementById("current-section").innerText = currentSection;
        const successHeading = document.getElementById("success-heading");
        successHeading.innerText = `You passed Section ${currentSection}: ${sectionNames[currentSection - 1]}`;
    }
  } else {
    showLoadingOverlay();
    document.getElementById("failure-container").style.display = "block";
    isFailureContainerDisplayed = true;

    const email = document.getElementById("email").value.trim();

    if (email) {
        try {
            const firstName = document.getElementById("first-name").value.trim();
            const lastName = document.getElementById("last-name").value.trim();
            const phone = document.getElementById("phone").value.trim();
            const role = document.getElementById("role").value.trim();

            // Create the assessment summary object
            const assessmentSummary = {
                timestamp: firebase.firestore.FieldValue.serverTimestamp(),
                totalScore: score,
                totalKnowledgeQuestions: totalKnowledgeQuestions,
                sectionScores: { ...sectionScores },
                questionsPerSection: { ...questionsPerSection },
                currentSection: currentSection,
                status: 'completed'
            };

            const resultData = {
                employeeEmail: email,
                section: sectionNames[currentSection - 1],
                score,
                role,
                totalQuestions: totalKnowledgeQuestions, // Only count knowledge questions for scoring
                isNewUser: false,
                timestamp: firebase.firestore.FieldValue.serverTimestamp(),
                firstName,
                lastName,
                userCompany: userCompany,
                userPhone: phone,
                assessmentType: 'softSkills' // Add assessment type
            };

            const companyRef = db.collection('companies').doc(userCompany);
            const userRef = companyRef.collection('users').doc(email);
            const assessmentResultsRef = userRef.collection('softSkillsAssessmentResults');

            // Save the assessment summary to a new collection
            await userRef.collection('softSkillsSummaries').add(assessmentSummary);
            console.log('Soft skills assessment summary saved to Firestore:', assessmentSummary);

            // Add the new assessment result
            await assessmentResultsRef.add(resultData);
            console.log('Assessment result saved to Firestore:', resultData);

            // Update the assessment status under the user document with soft skills specific fields
            await userRef.update({
                status: 'completed',
                lastSoftSkillsAssessmentId: assessmentResultsRef.id,
                lastSoftSkillsAssessmentDate: firebase.firestore.FieldValue.serverTimestamp()
            });

            hideLoadingOverlay();
            showFeedbackPrompt();
            initializePathwayButton();
            pollForRecommendations(email, userCompany);

            showNotification('Your results are being processed. You will be notified when complete.', 'success');

            sendAssessmentResult(email).then(() => {
                console.log('Assessment result sent for recommendations');
                showNotification('Your results have been processed successfully!', 'success');
            }).catch(error => {
                console.error('Error sending assessment result for recommendations:', error);
                showNotification('An error occurred while processing your results. Please contact support.', 'error');
            });

        } catch (error) {
            console.error('Error saving assessment data to Firestore:', error);
            hideLoadingOverlay();
            showNotification('An error occurred while submitting assessment data. Please try again or contact support.', 'error');
        }
    } else {
        console.log('Email is not provided, assessment data will not be saved.');
        hideLoadingOverlay();
    }
  }
};

      const logFinalResults = () => {
        const currentSectionName = sectionNames[currentSection - 1];

        // Filter responses to only include current section
        const currentSectionResponses = sessionLogs.userResponses.filter(response =>
            response.section === currentSectionName
        );

        const finalResults = {
            timestamp: new Date().toISOString(),
            userInfo: {
                email: document.getElementById("email").value.trim(),
                firstName: document.getElementById("first-name").value.trim(),
                lastName: document.getElementById("last-name").value.trim(),
                role: document.getElementById("role").value.trim(),
                company: userCompany
            },
            quizResults: {
                totalScore: score,
                sectionScores: { [currentSectionName]: sectionScores[currentSectionName] },
                questionsPerSection: { [currentSectionName]: questionsPerSection[currentSectionName] },
                currentSection: currentSection,
                sectionName: currentSectionName,
                totalQuestions: quizData.length,
                passThreshold: 0.7 * quizData.length,
                passed: score >= (0.7 * quizData.length)
            },
            completeSessionData: {
                framework: sessionLogs.framework,
                quiz: sessionLogs.quiz,
                userResponses: currentSectionResponses,
                finalTimestamp: new Date().toISOString()
            }
        };

        console.log('Final Quiz Results:', JSON.stringify(finalResults, null, 2));
        return finalResults;
    };

    // Progressive loading function for first batch
const loadQuizDataProgressive = async () => {
  showQuizLoadingOverlay();

  // Initialize progress tracking
  let currentProgress = 0;
  updateLoadingProgress(currentProgress);

  // Get role and section for background question checking
  const role = document.getElementById("role")?.value.trim();
  const currentSectionName = window.currentSection || 'foundational';

  // First, check if we have questions from background generation
  console.log('Checking for background-generated questions...');
  const backgroundQuestions = await checkBackgroundQuestions(role, currentSectionName);

  if (backgroundQuestions) {
    console.log('Using questions from background generation!');

    // Mark regular questions with a type
    const typedRegularQuestions = backgroundQuestions.regular.map(q => ({
      ...q,
      type: "knowledge-check"
    }));

    // Combine and shuffle questions
    const allQuestions = [...typedRegularQuestions, ...backgroundQuestions.selfAssessment];
    quizData = shuffleArray(allQuestions);

    // Cache the questions in client-side cache
    cacheQuestions(role, currentSectionName, 'regular', backgroundQuestions.regular);
    cacheQuestions(role, currentSectionName, 'self-assessment', backgroundQuestions.selfAssessment);

    // Animate progress to completion quickly
    await animateProgressTo(100, 500);

    // Display the first question immediately
    setTimeout(() => {
      hideQuizLoadingOverlay();
      loadQuestion();
      updateProgressBar();
      console.log('Quiz loaded instantly from background generation!');
    }, 200);

    return;
  }

  // Check if background generation is still in progress
  console.log('No cached questions found, checking if background generation is in progress...');
  const backgroundStatus = await checkBackgroundGenerationStatus(role);

  if (backgroundStatus && !backgroundStatus.completed) {
    console.log('Background generation in progress, waiting for completion...');
    const waitResult = await waitForBackgroundGeneration(role, currentSectionName);

    if (waitResult.success) {
      console.log('Background generation completed while waiting, using generated questions!');

      // Mark regular questions with a type
      const typedRegularQuestions = waitResult.questions.regular.map(q => ({
        ...q,
        type: "knowledge-check"
      }));

      // Combine and shuffle questions
      const allQuestions = [...typedRegularQuestions, ...waitResult.questions.selfAssessment];
      quizData = shuffleArray(allQuestions);

      // Cache the questions in client-side cache
      cacheQuestions(role, currentSectionName, 'regular', waitResult.questions.regular);
      cacheQuestions(role, currentSectionName, 'self-assessment', waitResult.questions.selfAssessment);

      // Animate progress to completion quickly
      await animateProgressTo(100, 500);

      // Display the first question immediately
      setTimeout(() => {
        hideQuizLoadingOverlay();
        loadQuestion();
        updateProgressBar();
        console.log('Quiz loaded from background generation after waiting!');
      }, 200);

      return;
    } else {
      console.log('Background generation timed out or failed, falling back to progressive loading...');
      // Continue with normal progressive loading below
    }
  }

  // Helper function to smoothly animate progress to a target value
  const animateProgressTo = (targetValue, duration = 800) => {
    return new Promise((resolve) => {
      const startValue = currentProgress;
      const startTime = Date.now();

      const animate = () => {
        const currentTime = Date.now();
        const elapsed = currentTime - startTime;

        if (elapsed < duration) {
          // Use easeInOutCubic for smoother animation
          const progress = elapsed / duration;
          const eased = progress < 0.5
            ? 4 * progress * progress * progress
            : 1 - Math.pow(-2 * progress + 2, 3) / 2;

          currentProgress = startValue + (targetValue - startValue) * eased;
          updateLoadingProgress(Math.round(currentProgress));
          requestAnimationFrame(animate);
        } else {
          currentProgress = targetValue;
          updateLoadingProgress(Math.round(currentProgress));
          resolve();
        }
      };
      requestAnimationFrame(animate);
    });
  };

  try {
    const role = document.getElementById("role").value.trim();
    const currentSectionName = sectionNames[currentSection - 1];

    showQuestionLoadingState();

    // Update loading state and progress for framework loading
    updateLoadingState(LoadingState.LOADING_FRAMEWORK);
    await animateProgressTo(15); // Framework loading: 0% -> 15%

    // Try to get the framework from our global cache first
    let framework = getCachedFramework(role);

    // If not in cache, try to get it from Firestore
    if (!framework) {
      console.log('Framework not found in cache, fetching from Firestore');
      const frameworkDoc = await db.collection('frameworks')
        .doc(role)
        .get();

      if (!frameworkDoc.exists) {
        throw new Error('Framework not found for role');
      }

      framework = frameworkDoc.data();

      // Cache it for future use
      cacheFramework(role, framework);
      await animateProgressTo(25); // Framework fetched: 15% -> 25%
    } else {
      console.log('Using cached framework for role:', role);
      await animateProgressTo(20); // Framework from cache: 15% -> 20%
    }

    // Get email for session tracking
    const email = document.getElementById("email")?.value.trim() || null;

    // Update loading state and progress for first batch
    updateLoadingState(LoadingState.LOADING_FIRST_BATCH);
    await animateProgressTo(35); // Starting API requests: -> 35%

    console.log('Starting progressive loading - first batch...');

    // Load first batch of questions (smaller batch for immediate display)
    const firstBatchSize = window.firstBatchSize || 4;

    const firstBatchPromises = [
      fetch('/api/generate-quiz', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          role,
          section: currentSectionName,
          framework,
          email,
          batchSize: Math.ceil(firstBatchSize * 0.6), // ~2-3 knowledge-check questions
          batchOffset: 0
        }),
      }),
      fetch('/api/generate-self-assessment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          role,
          section: currentSectionName,
          framework,
          email,
          batchSize: Math.floor(firstBatchSize * 0.4), // ~1-2 self-assessment questions
          batchOffset: 0
        }),
      })
    ];

    // Update progress to show requests are sent
    await animateProgressTo(50); // API requests sent: 35% -> 50%

    // Wait for first batch responses
    const [regularResponse, selfAssessmentResponse] = await Promise.all(firstBatchPromises);

    // Update loading state to show we're processing responses
    updateLoadingState(LoadingState.LOADING_SELF_ASSESSMENT);

    // Update progress to indicate we've received responses and are processing
    await animateProgressTo(70); // Responses received: 50% -> 70%

    // Process first batch responses
    if (!regularResponse.ok) {
      const errorData = await regularResponse.json().catch(() => ({}));
      throw new Error(`Server error (regular questions): ${errorData.error || regularResponse.statusText}`);
    }

    if (!selfAssessmentResponse.ok) {
      const errorData = await selfAssessmentResponse.json().catch(() => ({}));
      throw new Error(`Server error (self-assessment): ${errorData.error || selfAssessmentResponse.statusText}`);
    }

    const firstBatchRegular = await regularResponse.json();
    const firstBatchSelfAssessment = await selfAssessmentResponse.json();

    console.log(`First batch loaded: ${firstBatchRegular.length} regular + ${firstBatchSelfAssessment.length} self-assessment`);

    // Mark regular questions with a type
    const typedRegularQuestions = firstBatchRegular.map(q => ({
      ...q,
      type: "knowledge-check"
    }));

    // Combine and shuffle the first batch
    quizData = [...typedRegularQuestions, ...firstBatchSelfAssessment];
    quizData = shuffleArray(quizData);

    // Set up initial quiz state
    questionsPerSection[currentSectionName] = window.totalExpectedQuestions || 15;
    sectionScores[currentSectionName] = 0;

    document.getElementById("current-section").innerText = currentSection;
    document.getElementById("section-name").innerText = currentSectionName;

    // Log the first batch for tracking
    if (window.quizLogger && typeof window.quizLogger.logQuiz === 'function') {
      window.quizLogger.logQuiz(quizData);
    }

    // Update loading state to show we're finalizing
    updateLoadingState(LoadingState.LOADING_ADDITIONAL_BATCHES);

    // Update progress to indicate we're processing the data
    await animateProgressTo(85); // Processing questions: 70% -> 85%

    // Final progress update and UI display
    updateLoadingState(LoadingState.COMPLETE);
    await animateProgressTo(100); // Complete: 85% -> 100%

    console.log(`First batch ready: ${quizData.length} questions loaded`);

    // Display the first question immediately
    setTimeout(() => {
      // Hide loading overlay first
      hideQuizLoadingOverlay();

      // Then load the question and update the progress bar
      loadQuestion();
      updateProgressBar();

      // Mark that first batch is loaded
      window.firstBatchLoaded = true;

      // Start background loading of remaining questions
      startBackgroundLoading(role, currentSectionName, framework, email, firstBatchRegular.length, firstBatchSelfAssessment.length);

      console.log('First batch UI displayed successfully - background loading started');
    }, 300);

  } catch (error) {
    console.error('Error loading first batch:', error);

    // Fallback to full loading if progressive loading fails
    console.log('Falling back to full loading...');
    window.isProgressiveLoadingEnabled = false;

    // Hide the current loading overlay
    hideQuizLoadingOverlay();

    // Start full loading
    setTimeout(() => {
      loadQuizData();
    }, 500);
  }
};

    // Replace the loadQuizData function in quizFunctions.js
const loadQuizData = async () => {
  showQuizLoadingOverlay();

  // Initialize progress tracking
  let currentProgress = 0;
  updateLoadingProgress(currentProgress);

  // Get role and section for background question checking
  const role = document.getElementById("role")?.value.trim();
  const currentSectionName = window.currentSection || 'foundational';

  // First, check if we have questions from background generation
  console.log('Checking for background-generated questions...');
  const backgroundQuestions = await checkBackgroundQuestions(role, currentSectionName);

  if (backgroundQuestions) {
    console.log('Using questions from background generation!');

    // Mark regular questions with a type
    const typedRegularQuestions = backgroundQuestions.regular.map(q => ({
      ...q,
      type: "knowledge-check"
    }));

    // Combine and shuffle questions
    const allQuestions = [...typedRegularQuestions, ...backgroundQuestions.selfAssessment];
    quizData = shuffleArray(allQuestions);

    // Cache the questions in client-side cache
    cacheQuestions(role, currentSectionName, 'regular', backgroundQuestions.regular);
    cacheQuestions(role, currentSectionName, 'self-assessment', backgroundQuestions.selfAssessment);

    // Animate progress to completion quickly
    await animateProgressTo(100, 500);

    // Display the first question immediately
    setTimeout(() => {
      hideQuizLoadingOverlay();
      loadQuestion();
      updateProgressBar();
      console.log('Quiz loaded instantly from background generation!');
    }, 200);

    return;
  }

  // Check if background generation is still in progress
  console.log('No cached questions found, checking if background generation is in progress...');
  const backgroundStatus = await checkBackgroundGenerationStatus(role);

  if (backgroundStatus && !backgroundStatus.completed) {
    console.log('Background generation in progress, waiting for completion...');
    const waitResult = await waitForBackgroundGeneration(role, currentSectionName);

    if (waitResult.success) {
      console.log('Background generation completed while waiting, using generated questions!');

      // Mark regular questions with a type
      const typedRegularQuestions = waitResult.questions.regular.map(q => ({
        ...q,
        type: "knowledge-check"
      }));

      // Combine and shuffle questions
      const allQuestions = [...typedRegularQuestions, ...waitResult.questions.selfAssessment];
      quizData = shuffleArray(allQuestions);

      // Cache the questions in client-side cache
      cacheQuestions(role, currentSectionName, 'regular', waitResult.questions.regular);
      cacheQuestions(role, currentSectionName, 'self-assessment', waitResult.questions.selfAssessment);

      // Animate progress to completion quickly
      await animateProgressTo(100, 500);

      // Display the first question immediately
      setTimeout(() => {
        hideQuizLoadingOverlay();
        loadQuestion();
        updateProgressBar();
        console.log('Quiz loaded from background generation after waiting!');
      }, 200);

      return;
    } else {
      console.log('Background generation timed out or failed, falling back to standard loading...');
      // Continue with normal loading below
    }
  }

  // Helper function to smoothly animate progress to a target value
  const animateProgressTo = (targetValue, duration = 800) => {
    return new Promise((resolve) => {
      const startValue = currentProgress;
      const startTime = Date.now();

      const animate = () => {
        const currentTime = Date.now();
        const elapsed = currentTime - startTime;

        if (elapsed < duration) {
          // Use easeInOutCubic for smoother animation
          const progress = elapsed / duration;
          const eased = progress < 0.5
            ? 4 * progress * progress * progress
            : 1 - Math.pow(-2 * progress + 2, 3) / 2;

          currentProgress = startValue + (targetValue - startValue) * eased;
          updateLoadingProgress(Math.round(currentProgress));
          requestAnimationFrame(animate);
        } else {
          currentProgress = targetValue;
          updateLoadingProgress(Math.round(currentProgress));
          resolve();
        }
      };
      requestAnimationFrame(animate);
    });
  };

  try {
    const role = document.getElementById("role").value.trim();
    const currentSectionName = sectionNames[currentSection - 1];

    showQuestionLoadingState();

    // Update loading state and progress for framework loading
    updateLoadingState(LoadingState.LOADING_FRAMEWORK);
    await animateProgressTo(15); // Framework loading: 0% -> 15%

    // Try to get the framework from our global cache first
    let framework = getCachedFramework(role);

    // If not in cache, try to get it from Firestore
    if (!framework) {
      console.log('Framework not found in cache, fetching from Firestore');
      const frameworkDoc = await db.collection('frameworks')
        .doc(role)
        .get();

      if (!frameworkDoc.exists) {
        throw new Error('Framework not found for role');
      }

      framework = frameworkDoc.data();

      // Cache it for future use
      cacheFramework(role, framework);
      await animateProgressTo(25); // Framework fetched: 15% -> 25%
    } else {
      console.log('Using cached framework for role:', role);
      await animateProgressTo(20); // Framework from cache: 15% -> 20%
    }

    // Get email for session tracking
    const email = document.getElementById("email")?.value.trim() || null;

    // Check if requests are already pending
    const quizParams = { role, section: currentSectionName };
    const selfAssessParams = { role, section: currentSectionName };

    if (isRequestPending('generate-quiz', quizParams) ||
        isRequestPending('generate-self-assessment', selfAssessParams)) {
      console.log('Requests already pending, waiting for completion...');

      // Update loading state to show we're waiting for existing requests
      updateLoadingState(LoadingState.LOADING_FIRST_BATCH, {
        customMessage: 'Questions are being processed. Please wait a moment...',
        customSubmessage: 'Waiting for existing requests to complete'
      });

      // Update progress indicator to show we're waiting
      await animateProgressTo(40); // Waiting for pending requests: -> 40%

      // Poll for completion of the pending requests
      let waitTime = 0;
      const pollInterval = 2000; // Check every 2 seconds
      const maxWaitTime = 45000; // 45 seconds maximum wait time

      return new Promise((resolve, reject) => {
        const checkCompletion = setInterval(async () => {
          waitTime += pollInterval;

          if (!isRequestPending('generate-quiz', quizParams) &&
              !isRequestPending('generate-self-assessment', selfAssessParams)) {
            // Requests completed, try to get cached data
            clearInterval(checkCompletion);

            // Smoothly complete loading progress
            await animateProgressTo(100);

            setTimeout(() => {
              // Get the cached questions instead of making new API requests
              const cachedRegularQuestions = getCachedQuestions(role, currentSectionName);
              const cachedSelfAssessmentQuestions = getCachedSelfAssessmentQuestions(role, currentSectionName);

              if (cachedRegularQuestions && cachedSelfAssessmentQuestions) {
                console.log('Using cached questions from completed requests');

                // Mark regular questions with a type
                const typedRegularQuestions = cachedRegularQuestions.map(q => ({
                  ...q,
                  type: "knowledge-check"
                }));

                // Combine and shuffle the questions
                quizData = [...typedRegularQuestions, ...cachedSelfAssessmentQuestions];
                quizData = shuffleArray(quizData);

                questionsPerSection[currentSectionName] = quizData.length;
                sectionScores[currentSectionName] = 0;

                document.getElementById("current-section").innerText = currentSection;
                document.getElementById("section-name").innerText = currentSectionName;

                // Log the quiz data for tracking
                if (window.quizLogger && typeof window.quizLogger.logQuiz === 'function') {
                  window.quizLogger.logQuiz(quizData);
                }

                // Hide loading overlay
                hideQuizLoadingOverlay();

                // Load the question and update the progress bar
                loadQuestion();
                updateProgressBar();

                console.log('Quiz UI displayed successfully using cached data');
                resolve();
              } else {
                // If for some reason we don't have cached data, try again
                console.warn('Cached questions not found after request completion');
                hideQuizLoadingOverlay();
                loadQuizData().then(resolve).catch(reject);
              }
            }, 1200);

          } else if (waitTime >= maxWaitTime) {
            // Timeout reached, still pending
            clearInterval(checkCompletion);

            updateLoadingState(LoadingState.ERROR, {
              customMessage: 'Taking longer than expected. Retrying...',
              customSubmessage: 'Please wait while we retry'
            });

            // Show a message to the user
            showNotification('Questions are taking longer than expected to load. Retrying...', 'info');

            setTimeout(() => {
              hideQuizLoadingOverlay();
              loadQuizData().then(resolve).catch(reject);
            }, 1500);

          } else {
            // Still waiting, gradually increase progress but cap it
            const progressIncrement = Math.min(2, (85 - currentProgress) / 10);
            currentProgress = Math.min(85, currentProgress + progressIncrement);
            updateLoadingProgress(Math.round(currentProgress));

            if (waitTime > 10000) { // After 10 seconds show countdown
              updateLoadingState(LoadingState.LOADING_FIRST_BATCH, {
                customMessage: 'Still preparing questions...',
                customSubmessage: `${Math.round((maxWaitTime - waitTime)/1000)}s remaining`
              });
            }
          }
        }, pollInterval);
      });
    }

    // Mark requests as pending
    const quizRequestKey = markRequestPending('generate-quiz', quizParams);
    const selfAssessRequestKey = markRequestPending('generate-self-assessment', selfAssessParams);

    // Update loading state and progress for first batch
    updateLoadingState(LoadingState.LOADING_FIRST_BATCH);
    await animateProgressTo(35); // Starting API requests: -> 35%

    // Create state tracking variables for question loading
    let regularQuestionsReceived = false;
    let selfAssessmentQuestionsReceived = false;
    let regularQuestions = [];
    let selfAssessmentQuestions = [];

    console.log('Starting parallel fetch of both question types...');

    // Fetch both standard quiz questions and self-assessment questions in parallel
    // but handle each response separately to track when each type is received
    const regularQuestionsPromise = fetch('/api/generate-quiz', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        role,
        section: currentSectionName,
        framework,
        email  // Include email as session identifier
      }),
    });

    const selfAssessmentPromise = fetch('/api/generate-self-assessment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        role,
        section: currentSectionName,
        framework,
        email  // Include email as session identifier
      }),
    });

    // Update progress to show requests are sent
    await animateProgressTo(50); // API requests sent: 35% -> 50%

    // Wait for both promises to resolve
    const [regularQuestionsResponse, selfAssessmentResponse] = await Promise.all([regularQuestionsPromise, selfAssessmentPromise]);

    // Update loading state to show we're processing responses
    updateLoadingState(LoadingState.LOADING_SELF_ASSESSMENT);

    // Update progress to indicate we've received responses and are processing
    await animateProgressTo(70); // Responses received: 50% -> 70%

    // Process regular questions response
    if (!regularQuestionsResponse.ok) {
      const errorData = await regularQuestionsResponse.json().catch(() => ({}));
      throw new Error(`Server error (regular questions): ${errorData.error || regularQuestionsResponse.statusText}`);
    } else {
      console.log('Regular questions response received successfully');
      regularQuestions = await regularQuestionsResponse.json();
      regularQuestionsReceived = true;

      // Validate regular questions
      if (!Array.isArray(regularQuestions) || regularQuestions.length !== 10) {
        throw new Error('Invalid regular question data received');
      }
      console.log('Regular questions validated successfully');

      // Cache the regular questions
      cacheQuestions(role, currentSectionName, 'regular', regularQuestions);
    }

    // Process self-assessment questions response
    if (!selfAssessmentResponse.ok) {
      const errorData = await selfAssessmentResponse.json().catch(() => ({}));
      throw new Error(`Server error (self-assessment): ${errorData.error || selfAssessmentResponse.statusText}`);
    } else {
      console.log('Self-assessment questions response received successfully');
      selfAssessmentQuestions = await selfAssessmentResponse.json();
      selfAssessmentQuestionsReceived = true;

      // Validate self-assessment questions
      if (!Array.isArray(selfAssessmentQuestions) || selfAssessmentQuestions.length !== 5) {
        throw new Error('Invalid self-assessment question data received');
      }
      console.log('Self-assessment questions validated successfully');

      // Cache the self-assessment questions
      cacheQuestions(role, currentSectionName, 'self-assessment', selfAssessmentQuestions);
    }

    // Ensure both types of questions have been received before proceeding
    if (!regularQuestionsReceived || !selfAssessmentQuestionsReceived) {
      throw new Error('Failed to receive all required question types');
    }

    console.log('Both question types received and validated');

    // Update loading state to show we're finalizing
    updateLoadingState(LoadingState.LOADING_ADDITIONAL_BATCHES);

    // Update progress to indicate we're processing the data
    await animateProgressTo(85); // Processing questions: 70% -> 85%

    // Mark regular questions with a type
    const typedRegularQuestions = regularQuestions.map(q => ({
      ...q,
      type: "knowledge-check"
    }));

    // Combine and shuffle the questions
    quizData = [...typedRegularQuestions, ...selfAssessmentQuestions];
    quizData = shuffleArray(quizData);

    questionsPerSection[currentSectionName] = quizData.length;
    sectionScores[currentSectionName] = 0;

    document.getElementById("current-section").innerText = currentSection;
    document.getElementById("section-name").innerText = currentSectionName;

    // Log the quiz data for tracking
    if (window.quizLogger && typeof window.quizLogger.logQuiz === 'function') {
      window.quizLogger.logQuiz(quizData);
    }

    // Update progress to show UI preparation
    await animateProgressTo(95); // Preparing UI: 85% -> 95%

    console.log('All questions processed, preparing to display UI');
    console.log(`Total questions: ${quizData.length} (${typedRegularQuestions.length} knowledge check, ${selfAssessmentQuestions.length} self-assessment)`);

    // Final progress update and UI display
    updateLoadingState(LoadingState.COMPLETE);
    await animateProgressTo(100); // Complete: 95% -> 100%

    // Only now, AFTER all data is prepared, do we display the first question
    // This ensures the UI only loads once with the complete set of questions
    setTimeout(() => {
      // Hide loading overlay first
      hideQuizLoadingOverlay();

      // Then load the question and update the progress bar
      loadQuestion();
      updateProgressBar();

      // Mark requests as completed
      markRequestCompleted(quizRequestKey);
      markRequestCompleted(selfAssessRequestKey);

      console.log('Quiz UI displayed successfully');
    }, 300);

  } catch (error) {
    console.error('Error loading quiz data:', error);

    // If we marked requests as pending but they failed, release them
    if (typeof quizRequestKey !== 'undefined') {
      markRequestCompleted(quizRequestKey);
    }
    if (typeof selfAssessRequestKey !== 'undefined') {
      markRequestCompleted(selfAssessRequestKey);
    }

    const errorMessage = error.message.includes('Network error')
      ? 'Connection error. Please check your internet connection and try again.'
      : error.message === 'Requests already in progress. Please wait.'
        ? 'Questions are already being loaded. Please wait.'
        : 'Failed to load quiz questions. Please try again.';

    showNotification(errorMessage, 'error');

    document.getElementById("question").innerText = "Error loading questions. Please try again.";
    document.querySelectorAll('.option-btn').forEach(btn => {
      btn.disabled = true;
      btn.innerText = '-';
    });

    // Still need to hide loading overlay even on error
    hideQuizLoadingOverlay();
  }
};

// Helper function to shuffle an array (Fisher-Yates algorithm)
function shuffleArray(array) {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
}


      function showQuestionLoadingState() {
        document.getElementById("question").innerText = "Loading questions...";
        document.getElementById("progress-bar-fill").style.width = "0%";
        document.getElementById("progress-bar-text").innerText = "0%";

        const optionButtons = document.querySelectorAll('.option-btn');
        optionButtons.forEach(btn => {
          btn.disabled = true;
          btn.innerText = 'Loading...';
        });

        document.getElementById("skip-btn").disabled = true;
        document.getElementById("message").innerText = "";
      }

      const loadQuestion = () => {
        const questionObj = quizData[currentQuestion];
        document.getElementById("question").innerText = questionObj.question;

        // Get the options container
        const optionsContainer = document.getElementById("options-container");

        // Apply question type specific styling
        if (questionObj.type === "self-assessment") {
          optionsContainer.classList.add("self-assessment");
          // Show a subtle indicator for self-assessment questions
          document.getElementById("message").innerHTML = '<span class="self-assessment-indicator">Self-Assessment Question</span>';
        } else {
          optionsContainer.classList.remove("self-assessment");
          document.getElementById("message").innerText = "";
        }

        // Handle different number of options (3 for self-assessment, 4 for knowledge check)
        const optionCount = questionObj.type === "self-assessment" ? 3 : 4;

        // Display options
        for (let i = 0; i < 4; i++) {
          const btn = document.getElementById(`btn${i}`);

          if (i < optionCount) {
            btn.innerText = questionObj.options[i];
            btn.className = "option-btn";
            btn.dataset.type = questionObj.type;
            btn.dataset.level = i + 1; // Store level for self-assessment (1=basic, 2=intermediate, 3=advanced)
            btn.disabled = false;
            btn.style.opacity = 1;
            btn.style.cursor = "pointer";
            btn.style.display = "block";
          } else {
            // Hide extra buttons if not needed (for self-assessment with 3 options)
            btn.style.display = "none";
          }
        }

        document.getElementById("skip-btn").disabled = false;
        document.getElementById("skip-btn").style.opacity = 1;
        document.getElementById("skip-btn").style.cursor = "pointer";
      };

        const restartQuiz = () => {
        currentQuestion = 0;
        score = 0;
        document.getElementById("score").innerText = "0";
        document.getElementById("failure-container").style.display = "none";
        document.getElementById("start-page").style.display = "block";
        document.getElementById("progress-bar-fill").style.width = "0%";
        document.getElementById("progress-bar-text").innerText = "0%";
        loadQuizData();
      };


    // Toast notification function from the framework
    function showToast(message, duration = 5000) {
        const toast = document.createElement('div');
        toast.classList.add('toast');
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, duration);
    }

    function updateProgressBar() {
      // Use total expected questions for consistent progress calculation
      // This ensures progress bar doesn't jump when new batches are loaded
      const totalQuestions = window.totalExpectedQuestions || quizData.length || 15;
      const progress = (currentQuestion / totalQuestions) * 100;
      console.log('Progress:', progress, `(${currentQuestion}/${totalQuestions})`);

      // Update progress bar fill
      const progressBarFill = document.getElementById("progress-bar-fill");
      progressBarFill.style.width = `${progress}%`;

      // Check if progress text container exists
      let progressBarText = document.getElementById("progress-bar-text");

      if (!progressBarText) {
        // Create a new container for the progress text if it doesn't exist
        progressBarText = document.createElement("div");
        progressBarText.id = "progress-bar-text";
        progressBarText.className = "text-center text-gray-700 mb-2"; // Added proper styling classes

        // Get the progress bar container
        const progressBar = document.getElementById("progress-bar");
        // Insert the text BEFORE the progress bar
        progressBar.parentNode.insertBefore(progressBarText, progressBar);
      }

      // Update the progress text
      progressBarText.textContent = `Progress: ${Math.round(progress)}%`;
    }

    // Background loading function for remaining questions
    async function startBackgroundLoading(role, currentSectionName, framework, email, loadedRegularCount, loadedSelfAssessmentCount) {
      if (window.backgroundLoadingInProgress) {
        console.log('Background loading already in progress');
        return;
      }

      window.backgroundLoadingInProgress = true;
      console.log('Starting background loading for remaining questions...');

      try {
        const remainingRegular = 10 - loadedRegularCount;
        const remainingSelfAssessment = 5 - loadedSelfAssessmentCount;

        if (remainingRegular <= 0 && remainingSelfAssessment <= 0) {
          console.log('All questions already loaded');
          window.backgroundLoadingInProgress = false;
          return;
        }

        const backgroundPromises = [];

        // Load remaining regular questions if needed
        if (remainingRegular > 0) {
          backgroundPromises.push(
            fetch('/api/generate-quiz', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                role,
                section: currentSectionName,
                framework,
                email,
                batchSize: remainingRegular,
                batchOffset: loadedRegularCount
              }),
            })
          );
        }

        // Load remaining self-assessment questions if needed
        if (remainingSelfAssessment > 0) {
          backgroundPromises.push(
            fetch('/api/generate-self-assessment', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                role,
                section: currentSectionName,
                framework,
                email,
                batchSize: remainingSelfAssessment,
                batchOffset: loadedSelfAssessmentCount
              }),
            })
          );
        }

        // Wait for background responses
        const responses = await Promise.all(backgroundPromises);

        let backgroundRegular = [];
        let backgroundSelfAssessment = [];

        // Process responses with error handling
        let responseIndex = 0;
        if (remainingRegular > 0) {
          const regularResponse = responses[responseIndex++];
          if (regularResponse.ok) {
            try {
              backgroundRegular = await regularResponse.json();
              console.log(`Background loaded ${backgroundRegular.length} additional regular questions`);
            } catch (parseError) {
              console.error('Error parsing background regular questions:', parseError);
            }
          } else {
            console.warn(`Background regular questions request failed: ${regularResponse.status} ${regularResponse.statusText}`);
          }
        }

        if (remainingSelfAssessment > 0) {
          const selfAssessmentResponse = responses[responseIndex++];
          if (selfAssessmentResponse.ok) {
            try {
              backgroundSelfAssessment = await selfAssessmentResponse.json();
              console.log(`Background loaded ${backgroundSelfAssessment.length} additional self-assessment questions`);
            } catch (parseError) {
              console.error('Error parsing background self-assessment questions:', parseError);
            }
          } else {
            console.warn(`Background self-assessment questions request failed: ${selfAssessmentResponse.status} ${selfAssessmentResponse.statusText}`);
          }
        }

        // Add type to regular questions
        const typedBackgroundRegular = backgroundRegular.map(q => ({
          ...q,
          type: "knowledge-check"
        }));

        // Combine new questions
        const newQuestions = [...typedBackgroundRegular, ...backgroundSelfAssessment];

        if (newQuestions.length > 0) {
          // Shuffle new questions
          const shuffledNewQuestions = shuffleArray(newQuestions);

          // Append to existing quiz data
          quizData.push(...shuffledNewQuestions);

          console.log(`Background loading complete: ${newQuestions.length} additional questions loaded. Total: ${quizData.length}`);

          // Cache the complete question sets
          if (backgroundRegular.length > 0) {
            cacheQuestions(role, currentSectionName, 'regular', [...getCachedQuestions(role, currentSectionName) || [], ...backgroundRegular]);
          }
          if (backgroundSelfAssessment.length > 0) {
            cacheQuestions(role, currentSectionName, 'self-assessment', [...getCachedSelfAssessmentQuestions(role, currentSectionName) || [], ...backgroundSelfAssessment]);
          }
        } else {
          console.warn('Background loading failed to retrieve any additional questions');

          // If we have very few questions loaded, show a warning to the user
          if (quizData.length < 5) {
            console.warn('Very few questions loaded, user experience may be impacted');
            // Could show a toast notification here if needed
            if (window.showToast) {
              window.showToast('Some questions may be loading in the background. Your quiz experience will continue normally.', 'info');
            }
          }
        }

      } catch (error) {
        console.error('Background loading failed:', error);
        // Don't break the user experience - they can continue with the first batch
      } finally {
        window.backgroundLoadingInProgress = false;
      }
    }

    // Main loading function that chooses between progressive and full loading
    async function loadQuizDataMain() {
      // Add debugging information
      console.log('=== QUIZ LOADING START ===');
      console.log('Progressive loading enabled:', window.isProgressiveLoadingEnabled);
      console.log('Total expected questions:', window.totalExpectedQuestions);
      console.log('First batch size:', window.firstBatchSize);

      if (window.isProgressiveLoadingEnabled) {
        console.log('Using progressive loading strategy');
        await loadQuizDataProgressive();
      } else {
        console.log('Using full loading strategy');
        await loadQuizData();
      }

      console.log('=== QUIZ LOADING COMPLETE ===');
      console.log('Final quiz data length:', quizData.length);
      console.log('Question types:', quizData.map(q => q.type || 'knowledge-check'));
    }

    // Validation function to check progressive loading state
    function validateProgressiveLoading() {
      const validation = {
        totalExpectedQuestions: window.totalExpectedQuestions,
        actualQuestionsLoaded: quizData.length,
        firstBatchLoaded: window.firstBatchLoaded,
        backgroundLoadingInProgress: window.backgroundLoadingInProgress,
        progressiveLoadingEnabled: window.isProgressiveLoadingEnabled,
        questionTypes: quizData.reduce((acc, q) => {
          const type = q.type || 'knowledge-check';
          acc[type] = (acc[type] || 0) + 1;
          return acc;
        }, {}),
        cacheStatus: {
          frameworkCache: Object.keys(window.frameworkCache || {}).length,
          questionCache: Object.keys(window.questionCache || {}).length
        }
      };

      console.log('Progressive Loading Validation:', validation);
      return validation;
    }

    global.startQuiz = startQuiz;
    global.endQuiz = endQuiz;
    global.loadQuizData = loadQuizDataMain; // Use the main function that chooses loading strategy
    global.loadQuizDataProgressive = loadQuizDataProgressive;
    global.loadQuizDataFull = loadQuizData; // Keep the original as fallback
    global.startBackgroundLoading = startBackgroundLoading;
    global.validateProgressiveLoading = validateProgressiveLoading;
    global.loadQuestion = loadQuestion;
    global.updateProgressBar = updateProgressBar;
    global.restartQuiz = restartQuiz;
    global.logFinalResults = logFinalResults;
    global.checkBackgroundGenerationStatus = checkBackgroundGenerationStatus;
    global.checkBackgroundQuestions = checkBackgroundQuestions;
    global.startBackgroundGenerationMonitoring = startBackgroundGenerationMonitoring;
    global.showBackgroundGenerationStatus = showBackgroundGenerationStatus;
    global.hideBackgroundGenerationStatus = hideBackgroundGenerationStatus;
    global.updateBackgroundGenerationProgress = updateBackgroundGenerationProgress;
    global.showBackgroundGenerationComplete = showBackgroundGenerationComplete;
    global.waitForBackgroundGeneration = waitForBackgroundGeneration;
    window.showToast = showToast;
  })(typeof window !== 'undefined' ? window : global);
